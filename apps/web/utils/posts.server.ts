// Post-specific types using your actual schema
import { components } from "@/cms/schema";
import { payloadClient, Post } from "@/cms/PayloadCMSClient";
import { generateSlug } from "@/utils/slug";

type PostStatus = "draft" | "published";
type PostData = components["requestBodies"]["PostRequestBody"]["content"]["application/json"];
type CreatePostData = PostData;
type UpdatePostData = Partial<PostData>;

interface PostFilters {
  _status?: PostStatus;
  authors?: string; // Author ID
  categories?: string; // Category ID
  search?: string; // Search in title and content
  publishedAfter?: string;
  publishedBefore?: string;
}

interface PostListOptions {
  page?: number;
  limit?: number;
  sort?: 'title' | '-title' | 'publishedAt' | '-publishedAt' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
  depth?: number;
  locale?: string;
  filters?: PostFilters;
}

interface PostResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalDocs: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    nextPage?: number | null;
    prevPage?: number | null;
  };
}

// Utility functions for posts

/**
 * Get all published posts with optional filtering and pagination
 */
export async function getPublishedPosts(options: PostListOptions = {}): Promise<PostResult<Post[]>> {
  const {
    page = 1,
    limit = 10,
    sort = '-publishedAt',
    depth = 1,
    locale,
    filters = {}
  } = options;

  // Build where clause using your schema structure
  const where: any = {
    _status: { equals: 'published' },
    publishedAt: { exists: true }
  };

  // Add filters using proper schema query operations
  if (filters.authors) {
    where.authors = { in: filters.authors };
  }

  if (filters.categories) {
    where.categories = { in: filters.categories };
  }

  if (filters.search) {
    where.or = [
      { title: { contains: filters.search } },
      // Note: content search might need to be handled differently depending on your rich text structure
    ];
  }

  if (filters.publishedAfter) {
    where.publishedAt = { ...where.publishedAt, greater_than_equal: filters.publishedAfter };
  }

  if (filters.publishedBefore) {
    where.publishedAt = { ...where.publishedAt, less_than_equal: filters.publishedBefore };
  }

  try {
    const response = await payloadClient.getPosts({
      page,
      limit,
      sort,
      depth,
      locale,
      where
    });

    return {
      success: true,
      data: response.docs,
      pagination: {
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages,
        totalDocs: response.totalDocs,
        hasNextPage: response.hasNextPage,
        hasPrevPage: response.hasPrevPage,
        nextPage: response.nextPage,
        prevPage: response.prevPage
      }
    };
  } catch (error) {
    console.error('Error fetching published posts:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get all posts (including drafts) - admin only
 */
export async function getAllPosts(options: PostListOptions = {}): Promise<PostResult<Post[]>> {
  const {
    page = 1,
    limit = 10,
    sort = '-updatedAt',
    depth = 1,
    locale,
    filters = {}
  } = options;

  const where: any = {};

  // Add filters
  if (filters._status) {
    where._status = { equals: filters._status };
  }

  if (filters.authors) {
    where.authors = { in: filters.authors };
  }

  if (filters.categories) {
    where.categories = { in: filters.categories };
  }

  if (filters.search) {
    where.or = [
      { title: { contains: filters.search } }
    ];
  }

  try {
    const response = await payloadClient.getPosts({
      page,
      limit,
      sort,
      depth,
      locale,
      where: Object.keys(where).length > 0 ? where : undefined
    });

    return {
      success: true,
      data: response.docs,
      pagination: {
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages,
        totalDocs: response.totalDocs,
        hasNextPage: response.hasNextPage,
        hasPrevPage: response.hasPrevPage,
        nextPage: response.nextPage,
        prevPage: response.prevPage
      }
    };
  } catch (error) {
    console.error('Error fetching all posts:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get a single post by ID
 */
export async function getPostById(id: string, depth: number = 2, locale?: string): Promise<PostResult<Post>> {
  try {
    const post = await payloadClient.getPost(id, { depth, locale });
    return {
      success: true,
      data: post
    };
  } catch (error) {
    console.error(`Error fetching post ${id}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Post not found'
    };
  }
}

/**
 * Get a single published post by slug
 */
export async function getPublishedPostBySlug(slug: string, depth: number = 2, locale?: string): Promise<PostResult<Post>> {
  try {
    const response = await payloadClient.getPosts({
      where: {
        slug: { equals: slug },
        _status: { equals: 'published' },
        publishedAt: { exists: true }
      },
      limit: 1,
      depth,
      locale
    });

    if (response.docs.length === 0) {
      return {
        success: false,
        error: 'Post not found'
      };
    }

    return {
      success: true,
      data: response.docs[0]
    };
  } catch (error) {
    console.error(`Error fetching post by slug ${slug}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Post not found'
    };
  }
}

/**
 * Create a new post
 */
export async function createPost(data: CreatePostData, depth: number = 2, locale?: string): Promise<PostResult<Post>> {
  try {
    // Generate slug if not provided
    if (!data.slug && data.title) {
      data.slug = generateSlug(data.title);
    }

    // Set publishedAt if status is published and publishedAt is not set
    if (data._status === 'published' && !data.publishedAt) {
      data.publishedAt = new Date().toISOString();
    }

    const result = await payloadClient.createPost(data, { depth, locale });

    return {
      success: true,
      data: result.doc
    };
  } catch (error) {
    console.error('Error creating post:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create post'
    };
  }
}

/**
 * Get initial posts for blog page (first page with limited posts)
 * This is optimized for the initial blog page load
 */
export async function getInitialPosts(options: Omit<PostListOptions, 'page'> = {}): Promise<PostResult<Post[]>> {
  const {
    limit = 6, // Default to 6 posts for initial load
    sort = '-publishedAt',
    depth = 1,
    locale,
    filters = {}
  } = options;

  // Use the existing getPublishedPosts method with page = 1
  return getPublishedPosts({
    page: 1,
    limit,
    sort,
    depth,
    locale,
    filters
  });
}

/**
 * Update an existing post
 */
export async function updatePost(id: string, data: UpdatePostData, depth: number = 2, locale?: string): Promise<PostResult<Post>> {
  try {
    // Update slug if title changed
    if (data.title && !data.slug) {
      data.slug = generateSlug(data.title);
    }

    // Set publishedAt if status changed to published and publishedAt is not set
    if (data._status === 'published' && !data.publishedAt) {
      // First get the current post to check if it already has publishedAt
      const currentPost = await getPostById(id, 0);
      if (currentPost.success && !currentPost.data?.publishedAt) {
        data.publishedAt = new Date().toISOString();
      }
    }

    const post = await payloadClient.updatePost(id, data, { depth, locale });

    return {
      success: true,
      data: post
    };
  } catch (error) {
    console.error(`Error updating post ${id}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update post'
    };
  }
}
