"use client"

import { useState } from "react"
import { SocialShare } from "@/components/social-share"
import { PageErrorBoundary } from "@/components/error-boundary"
import { LoadMoreButton } from "@/components/load-more-button"
import { SectionLoading } from "@/components/loading-spinner"
import { clientEnv } from "@/utils/environment.client";

const allBlogPosts = [
  // Example blog posts
  { id: 1, title: "Post 1", content: "Content of post 1" },
  { id: 2, title: "Post 2", content: "Content of post 2" },
  { id: 3, title: "Post 3", content: "Content of post 3" },
  { id: 4, title: "Post 4", content: "Content of post 4" },
  { id: 5, title: "Post 5", content: "Content of post 5" },
  { id: 6, title: "Post 6", content: "Content of post 6" },
  { id: 7, title: "Post 7", content: "Content of post 7" },
  { id: 8, title: "Post 8", content: "Content of post 8" },
]

export default function BlogPage() {
  const [displayedPosts, setDisplayedPosts] = useState(allBlogPosts.slice(0, 6))
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(allBlogPosts.length > 6)
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  useState(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 800)
    return () => clearTimeout(timer)
  })

  const handleLoadMore = () => {
    setIsLoading(true)
    // Simulate loading delay
    setTimeout(() => {
      const currentLength = displayedPosts.length
      const nextPosts = allBlogPosts.slice(currentLength, currentLength + 3)
      setDisplayedPosts([...displayedPosts, ...nextPosts])
      setHasMore(currentLength + nextPosts.length < allBlogPosts.length)
      setIsLoading(false)
    }, 1000)
  }

  if (isInitialLoading) {
    return <SectionLoading text="Loading blog posts..." />
  }
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('CLIENT NODE_ENV:', clientEnv('NODE_ENV'));
  console.log('BASE_URL:', clientEnv('NEXT_PUBLIC_BASE_URL'));
  // Note: Server-side functions like getPostById should be called in server components or API routes

  return (
    <PageErrorBoundary>
      <div className="min-h-screen bg-background pt-20">
        {/* Display blog posts */}
        {displayedPosts.map((post) => (
          <div key={post.id} className="mb-8">
            <h2 className="text-2xl font-bold">{post.title}</h2>
            <p className="text-gray-600">{post.content}</p>
            <SocialShare title={post.title} description={post.content} />
          </div>
        ))}
        {/* Load more button */}
        {hasMore && <LoadMoreButton onClick={handleLoadMore} isLoading={isLoading} />}
      </div>
    </PageErrorBoundary>
  )
}
