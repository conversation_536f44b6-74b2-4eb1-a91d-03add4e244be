import { PageErrorBoundary } from "@/components/error-boundary"
import { getPublishedPosts } from "@/utils/posts.server"
import { env } from "@/utils/environment.server"
import { BlogPostClient } from "@/components/blog-post-client"

const allBlogPosts = [
  // Example blog posts - fallback data
  { id: 1, title: "Post 1", content: "Content of post 1" },
  { id: 2, title: "Post 2", content: "Content of post 2" },
  { id: 3, title: "Post 3", content: "Content of post 3" },
  { id: 4, title: "Post 4", content: "Content of post 4" },
  { id: 5, title: "Post 5", content: "Content of post 5" },
  { id: 6, title: "Post 6", content: "Content of post 6" },
  { id: 7, title: "Post 7", content: "Content of post 7" },
  { id: 8, title: "Post 8", content: "Content of post 8" },
]

export default async function BlogPage() {
  // Server-side environment access works here
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('BASE_URL:', env('BASE_URL'));
  console.log('CMS_BASE_URL:', env('CMS_BASE_URL'));

  // Fetch initial posts from CMS
  const postsResult = await getPublishedPosts({
    page: 1,
    limit: 6,
    depth: 1
  });

  const initialPosts = postsResult.success ? postsResult.data : [];
  const hasMorePosts = postsResult.success && postsResult.pagination ?
    postsResult.pagination.hasNextPage : false;

  return (
    <PageErrorBoundary>
      <div className="min-h-screen bg-background pt-20">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold mb-8">Blog</h1>

          {/* Use client component for interactive features */}
          <BlogPostClient
            initialPosts={initialPosts}
            fallbackPosts={allBlogPosts}
            hasMoreInitially={hasMorePosts}
          />
        </div>
      </div>
    </PageErrorBoundary>
  )
}
