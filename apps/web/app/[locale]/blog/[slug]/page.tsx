import { notFound } from 'next/navigation'
import { getPublishedPostBySlug } from '@/utils/posts.server'
import { SocialShare } from '@/components/social-share'
import { PageErrorBoundary } from '@/components/error-boundary'

interface BlogPostPageProps {
  params: Promise<{
    locale: string
    slug: string
  }>
}

// Helper function to render rich text content
function renderRichText(content: any): JSX.Element {
  if (!content?.root?.children) {
    return <div>No content available</div>
  }

  const renderChildren = (children: any[]): JSX.Element[] => {
    return children.map((child, index) => {
      if (child.type === 'paragraph') {
        return (
          <p key={index} className="mb-4 text-gray-700 leading-relaxed">
            {child.children && renderChildren(child.children)}
          </p>
        )
      }
      
      if (child.type === 'heading') {
        const HeadingTag = `h${child.tag}` as keyof JSX.IntrinsicElements
        const headingClasses = {
          h1: 'text-3xl font-bold mb-6 mt-8',
          h2: 'text-2xl font-bold mb-4 mt-6',
          h3: 'text-xl font-bold mb-3 mt-5',
          h4: 'text-lg font-bold mb-2 mt-4',
          h5: 'text-base font-bold mb-2 mt-3',
          h6: 'text-sm font-bold mb-2 mt-2'
        }
        
        return (
          <HeadingTag key={index} className={headingClasses[HeadingTag as keyof typeof headingClasses]}>
            {child.children && renderChildren(child.children)}
          </HeadingTag>
        )
      }
      
      if (child.type === 'list') {
        const ListTag = child.listType === 'bullet' ? 'ul' : 'ol'
        return (
          <ListTag key={index} className="mb-4 ml-6 space-y-2">
            {child.children && renderChildren(child.children)}
          </ListTag>
        )
      }
      
      if (child.type === 'listitem') {
        return (
          <li key={index} className="text-gray-700">
            {child.children && renderChildren(child.children)}
          </li>
        )
      }
      
      if (child.type === 'link') {
        return (
          <a 
            key={index} 
            href={child.url} 
            className="text-blue-600 hover:text-blue-800 underline"
            target={child.newTab ? '_blank' : undefined}
            rel={child.newTab ? 'noopener noreferrer' : undefined}
          >
            {child.children && renderChildren(child.children)}
          </a>
        )
      }
      
      // Handle text nodes
      if (child.text !== undefined) {
        let textElement = child.text
        
        if (child.bold) {
          textElement = <strong key={index}>{textElement}</strong>
        }
        if (child.italic) {
          textElement = <em key={index}>{textElement}</em>
        }
        if (child.underline) {
          textElement = <u key={index}>{textElement}</u>
        }
        if (child.strikethrough) {
          textElement = <s key={index}>{textElement}</s>
        }
        
        return textElement
      }
      
      // Fallback for unknown types
      return <span key={index}>{JSON.stringify(child)}</span>
    })
  }

  return <div>{renderChildren(content.root.children)}</div>
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { locale, slug } = await params
  
  // Fetch the post by slug
  const postResult = await getPublishedPostBySlug(slug, 2, locale)
  
  if (!postResult.success || !postResult.data) {
    notFound()
  }
  
  const post = postResult.data
  
  // Format published date
  const publishedDate = post.publishedAt 
    ? new Date(post.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null

  return (
    <PageErrorBoundary>
      <article className="min-h-screen bg-background pt-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Hero Image */}
            {post.heroImage && typeof post.heroImage === 'object' && post.heroImage.url && (
              <div className="mb-8">
                <img
                  src={post.heroImage.url}
                  alt={post.heroImage.alt || post.title}
                  className="w-full h-96 object-cover rounded-lg"
                />
              </div>
            )}

            {/* Post Header */}
            <header className="mb-8">
              <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
              
              {/* Meta information */}
              <div className="flex items-center gap-6 text-gray-600 mb-6">
                {publishedDate && (
                  <time dateTime={post.publishedAt || undefined}>
                    {publishedDate}
                  </time>
                )}
                
                {/* Authors */}
                {post.populatedAuthors && post.populatedAuthors.length > 0 && (
                  <div className="flex items-center gap-2">
                    <span>by</span>
                    {post.populatedAuthors.map((author, index) => (
                      <span key={author.id}>
                        {author.name}
                        {index < post.populatedAuthors!.length - 1 && ", "}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Categories */}
              {post.categories && post.categories.length > 0 && (
                <div className="flex items-center gap-2 mb-6">
                  {post.categories.map((category) => (
                    <span 
                      key={typeof category === 'string' ? category : category.id}
                      className="bg-gray-100 px-3 py-1 rounded-full text-sm"
                    >
                      {typeof category === 'string' ? category : category.title}
                    </span>
                  ))}
                </div>
              )}
            </header>

            {/* Post Content */}
            <div className="prose prose-lg max-w-none mb-12">
              {renderRichText(post.content)}
            </div>

            {/* Social Share */}
            <div className="border-t pt-8 mb-8">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Share this post</h3>
                <SocialShare 
                  title={post.title} 
                  description={post.meta?.description || ''}
                />
              </div>
            </div>

            {/* Related Posts */}
            {post.relatedPosts && post.relatedPosts.length > 0 && (
              <section className="border-t pt-8">
                <h3 className="text-2xl font-bold mb-6">Related Posts</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {post.relatedPosts.map((relatedPost) => {
                    const related = typeof relatedPost === 'string' ? null : relatedPost
                    if (!related) return null
                    
                    return (
                      <a 
                        key={related.id}
                        href={`/blog/${related.slug}`}
                        className="block p-4 border rounded-lg hover:shadow-md transition-shadow"
                      >
                        <h4 className="font-semibold mb-2">{related.title}</h4>
                        {related.meta?.description && (
                          <p className="text-gray-600 text-sm">{related.meta.description}</p>
                        )}
                      </a>
                    )
                  })}
                </div>
              </section>
            )}
          </div>
        </div>
      </article>
    </PageErrorBoundary>
  )
}
