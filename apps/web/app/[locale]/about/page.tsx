import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, Coffee, Camera, Book, Mail, MapPin } from "lucide-react"

const values = [
  {
    icon: Heart,
    title: "Authentic Living",
    description: "Embracing imperfection and finding beauty in the real, unfiltered moments of everyday life.",
  },
  {
    icon: Coffee,
    title: "Mindful Moments",
    description: "Slowing down to savor the simple pleasures and creating space for what truly matters.",
  },
  {
    icon: Camera,
    title: "Creative Expression",
    description: "Believing that everyone has a unique creative voice worth sharing with the world.",
  },
  {
    icon: Book,
    title: "Continuous Growth",
    description: "Learning, evolving, and staying curious about life's endless possibilities.",
  },
]

const timeline = [
  {
    year: "2020",
    title: "The Beginning",
    description: "Started sharing my journey of mindful living and creative exploration on social media.",
  },
  {
    year: "2021",
    title: "Finding My Voice",
    description: "Launched my first blog posts about seasonal living and discovered my passion for storytelling.",
  },
  {
    year: "2022",
    title: "Building Community",
    description: "Created online workshops and began fostering connections with like-minded souls around the world.",
  },
  {
    year: "2023",
    title: "Conversations & Reflections",
    description: "Started my podcast to dive deeper into meaningful conversations about creativity and purpose.",
  },
  {
    year: "2024",
    title: "Time With Tuuli",
    description: "Launched this space as a home for all the things I love: stories, recipes, finds, and community.",
  },
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-background to-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground mb-6">
                Hello, I'm <span className="text-primary">Tuuli</span>
              </h1>
              <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                Welcome to my little corner of the internet, where I share the things that bring meaning to my days:
                mindful living, creative pursuits, nourishing recipes, and the beauty found in ordinary moments.
              </p>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                I believe that life is meant to be savored slowly, that creativity lives in all of us, and that the most
                profound joy often comes from the simplest things.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8">
                  <Mail className="h-4 w-4 mr-2" />
                  Get in Touch
                </Button>
                <Button variant="outline" className="rounded-xl bg-transparent">
                  <Coffee className="h-4 w-4 mr-2" />
                  Buy Me a Coffee
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="relative rounded-3xl overflow-hidden shadow-soft-hover">
                <img
                  src="/placeholder.jpg"
                  alt="Tuuli in her creative space"
                  className="w-full h-auto object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent"></div>
              </div>
              <div className="absolute -bottom-6 -right-6 bg-accent/90 rounded-full p-4 shadow-soft">
                <Heart className="h-8 w-8 text-white" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">What I Believe In</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              These values guide everything I create and share, from the recipes I develop to the conversations I have.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card
                key={index}
                className="border-0 shadow-soft rounded-2xl text-center hover:shadow-soft-hover transition-shadow duration-300"
              >
                <CardContent className="p-8">
                  <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <value.icon className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-3">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* My Story Timeline */}
      <section className="py-16 bg-secondary/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">My Journey</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              From quiet beginnings to building this beautiful community, here's how Time With Tuuli came to life.
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {timeline.map((item, index) => (
                <div key={index} className="flex gap-8 items-start">
                  <div className="flex-shrink-0">
                    <div className="bg-primary text-primary-foreground rounded-full w-16 h-16 flex items-center justify-center font-bold text-sm">
                      {item.year}
                    </div>
                  </div>
                  <div className="flex-1">
                    <Card className="border-0 shadow-soft rounded-2xl">
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold text-foreground mb-2">{item.title}</h3>
                        <p className="text-muted-foreground leading-relaxed">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Personal Details */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-6">A Little More About Me</h2>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  When I'm not creating content, you'll find me curled up with a good book and a cup of tea, tending to
                  my small herb garden, or experimenting with new recipes in my kitchen. I have an endless fascination
                  with how different cultures approach food, wellness, and creativity.
                </p>
                <p>
                  I'm originally from Estonia, where I learned to appreciate the changing seasons and the importance of
                  creating cozy spaces. These days, I split my time between writing, recipe development, and having
                  meaningful conversations with incredible people for my podcast.
                </p>
                <p>
                  My greatest joy comes from connecting with others who share a love for intentional living, whether
                  that's through a perfectly imperfect homemade meal, a quiet morning ritual, or a creative project that
                  brings you alive.
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <Card className="border-0 shadow-soft rounded-2xl">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Quick Facts</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">Based in Tallinn, Estonia</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Coffee className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">Favorite drink: Oat milk matcha latte</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Book className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">
                        Currently reading: "The Seven Husbands of Evelyn Hugo"
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Camera className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">Loves: Golden hour photography</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft rounded-2xl">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Current Favorites</h3>
                  <div className="space-y-3">
                    <div>
                      <Badge variant="outline" className="mb-2">
                        Book
                      </Badge>
                      <p className="text-sm text-muted-foreground">"Braiding Sweetgrass" by Robin Wall Kimmerer</p>
                    </div>
                    <div>
                      <Badge variant="outline" className="mb-2">
                        Recipe
                      </Badge>
                      <p className="text-sm text-muted-foreground">Cardamom rose cookies with Earl Grey tea</p>
                    </div>
                    <div>
                      <Badge variant="outline" className="mb-2">
                        Ritual
                      </Badge>
                      <p className="text-sm text-muted-foreground">Morning pages with coffee at sunrise</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Connect Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-foreground mb-4">Let's Stay Connected</h2>
            <p className="text-muted-foreground mb-8 leading-relaxed">
              I'd love to hear from you! Whether you have a question, want to share your own story, or just want to say
              hello, my inbox is always open.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8">
                <Mail className="h-4 w-4 mr-2" />
                Send Me an Email
              </Button>
              <Button variant="outline" className="rounded-xl bg-transparent">
                <Heart className="h-4 w-4 mr-2" />
                Follow My Journey
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
