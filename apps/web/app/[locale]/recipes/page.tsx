"use client"

import { useTranslations } from "next-intl"
import { Clock, Users, ChefHat, Heart, Star, Filter, Search } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ScrollAnimation } from "@/components/scroll-animation"
import { SocialShare } from "@/components/social-share"
import { RecipeRating } from "@/components/recipe-rating"
import { useState } from "react"

export default function RecipesPage() {
  const t = useTranslations("recipes")
  const [visibleRecipes, setVisibleRecipes] = useState(6)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")

  const recipes = [
    {
      id: 1,
      title: t("recipes.autumn_soup.title"),
      description: t("recipes.autumn_soup.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "45 min",
      servings: 4,
      difficulty: t("difficulty.easy"),
      category: t("categories.soups"),
      rating: 4.8,
      reviews: 124,
      isVegetarian: true,
      ingredients: 8,
    },
    {
      id: 2,
      title: t("recipes.sourdough_bread.title"),
      description: t("recipes.sourdough_bread.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "4 hours",
      servings: 8,
      difficulty: t("difficulty.advanced"),
      category: t("categories.baking"),
      rating: 4.9,
      reviews: 89,
      isVegetarian: true,
      ingredients: 5,
    },
    {
      id: 3,
      title: t("recipes.herb_salad.title"),
      description: t("recipes.herb_salad.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "15 min",
      servings: 2,
      difficulty: t("difficulty.easy"),
      category: t("categories.salads"),
      rating: 4.6,
      reviews: 67,
      isVegetarian: true,
      ingredients: 12,
    },
    {
      id: 4,
      title: t("recipes.winter_stew.title"),
      description: t("recipes.winter_stew.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "2 hours",
      servings: 6,
      difficulty: t("difficulty.medium"),
      category: t("categories.mains"),
      rating: 4.7,
      reviews: 156,
      isVegetarian: false,
      ingredients: 15,
    },
    {
      id: 5,
      title: t("recipes.berry_tart.title"),
      description: t("recipes.berry_tart.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "1.5 hours",
      servings: 8,
      difficulty: t("difficulty.medium"),
      category: t("categories.desserts"),
      rating: 4.9,
      reviews: 203,
      isVegetarian: true,
      ingredients: 10,
    },
    {
      id: 6,
      title: t("recipes.morning_smoothie.title"),
      description: t("recipes.morning_smoothie.description"),
      image: "/placeholder.svg?height=300&width=400",
      cookTime: "5 min",
      servings: 1,
      difficulty: t("difficulty.easy"),
      category: t("categories.drinks"),
      rating: 4.5,
      reviews: 92,
      isVegetarian: true,
      ingredients: 6,
    },
  ]

  const categories = [
    { id: "all", name: t("categories.all") },
    { id: "soups", name: t("categories.soups") },
    { id: "mains", name: t("categories.mains") },
    { id: "salads", name: t("categories.salads") },
    { id: "desserts", name: t("categories.desserts") },
    { id: "baking", name: t("categories.baking") },
    { id: "drinks", name: t("categories.drinks") },
  ]

  const filteredRecipes = recipes.filter((recipe) => {
    const matchesSearch = recipe.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory =
      selectedCategory === "all" || recipe.category === categories.find((c) => c.id === selectedCategory)?.name
    return matchesSearch && matchesCategory
  })

  const loadMoreRecipes = () => {
    setVisibleRecipes((prev) => Math.min(prev + 6, filteredRecipes.length))
  }

  return (
    <div className="min-h-screen bg-off-white">
      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Hero Section */}
        <ScrollAnimation>
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-sand-100 rounded-full">
                <ChefHat className="w-12 h-12 text-sand-600" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">{t("hero.title")}</h1>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">{t("hero.description")}</p>
          </div>
        </ScrollAnimation>

        {/* Search and Filter */}
        <ScrollAnimation>
          <div className="mb-12">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  placeholder={t("search.placeholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-slate-200 focus:border-teal-500"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-slate-500" />
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category.id}
                      size="sm"
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`rounded-full ${
                        selectedCategory === category.id
                          ? "bg-teal-600 hover:bg-teal-700 text-white"
                          : "border-slate-200 text-slate-600 hover:bg-slate-50"
                      }`}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </ScrollAnimation>

        {/* Recipes Grid */}
        <ScrollAnimation>
          <div className="mb-12">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredRecipes.slice(0, visibleRecipes).map((recipe) => (
                <Card
                  key={recipe.id}
                  className="border-0 shadow-soft bg-white hover:shadow-lg transition-shadow overflow-hidden"
                >
                  <div className="relative">
                    <img
                      src={recipe.image || "/placeholder.svg"}
                      alt={recipe.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4 flex space-x-2">
                      <Badge className="bg-white bg-opacity-90 text-slate-700">{recipe.category}</Badge>
                      {recipe.isVegetarian && (
                        <Badge className="bg-green-100 text-green-700">{t("badges.vegetarian")}</Badge>
                      )}
                    </div>
                    <div className="absolute top-4 right-4">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="bg-white bg-opacity-90 hover:bg-white rounded-full p-2"
                      >
                        <Heart className="w-4 h-4 text-slate-600" />
                      </Button>
                    </div>
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between text-white text-sm">
                        <div className="flex items-center bg-black bg-opacity-70 px-2 py-1 rounded">
                          <Clock className="w-3 h-3 mr-1" />
                          {recipe.cookTime}
                        </div>
                        <div className="flex items-center bg-black bg-opacity-70 px-2 py-1 rounded">
                          <Users className="w-3 h-3 mr-1" />
                          {recipe.servings}
                        </div>
                      </div>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-slate-800 mb-2 line-clamp-2">{recipe.title}</h3>
                    <p className="text-sm text-slate-600 mb-4 line-clamp-3">{recipe.description}</p>

                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4 text-xs text-slate-500">
                        <span>{recipe.difficulty}</span>
                        <span>
                          {recipe.ingredients} {t("ingredients")}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium text-slate-700">{recipe.rating}</span>
                        <span className="text-xs text-slate-500">({recipe.reviews})</span>
                      </div>
                    </div>

                    <RecipeRating recipeId={recipe.id} averageRating={recipe.rating} recipeName={recipe.title} reviews={[]} totalReviews={recipe.reviews} />

                    <div className="flex items-center space-x-2 mt-4">
                      <Button className="flex-1 bg-sand-600 hover:bg-sand-700 text-white rounded-full">
                        {t("actions.view_recipe")}
                      </Button>
                      <SocialShare
                        url={`/recipes/${recipe.id}`}
                        title={recipe.title}
                        description={recipe.description}
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {visibleRecipes < filteredRecipes.length && (
              <div className="text-center mt-12">
                <Button
                  onClick={loadMoreRecipes}
                  variant="outline"
                  className="px-8 py-3 rounded-full border-sand-200 text-sand-600 hover:bg-sand-50 bg-transparent"
                >
                  {t("actions.load_more")}
                </Button>
              </div>
            )}
          </div>
        </ScrollAnimation>
      </div>
    </div>
  )
}
