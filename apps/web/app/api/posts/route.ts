import { NextRequest, NextResponse } from 'next/server'
import { getPublishedPosts } from '@/utils/posts.server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1', 10)
    const limit = parseInt(searchParams.get('limit') || '6', 10)
    const locale = searchParams.get('locale') || undefined
    const sort = searchParams.get('sort') as any || '-publishedAt'
    const depth = parseInt(searchParams.get('depth') || '2', 10)
    
    // Extract filter parameters
    const authors = searchParams.get('authors') || undefined
    const categories = searchParams.get('categories') || undefined
    const search = searchParams.get('search') || undefined
    const publishedAfter = searchParams.get('publishedAfter') || undefined
    const publishedBefore = searchParams.get('publishedBefore') || undefined

    // Build filters object
    const filters: any = {}
    if (authors) filters.authors = authors
    if (categories) filters.categories = categories
    if (search) filters.search = search
    if (publishedAfter) filters.publishedAfter = publishedAfter
    if (publishedBefore) filters.publishedBefore = publishedBefore

    // Fetch posts using the server utility
    const result = await getPublishedPosts({
      page,
      limit,
      sort,
      depth,
      locale,
      filters: Object.keys(filters).length > 0 ? filters : undefined
    })

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to fetch posts' 
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })

  } catch (error) {
    console.error('API Error fetching posts:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
