import { NextResponse } from 'next/server'
import { checkCM<PERSON>Health, getCMSConfig } from '@/utils/cms-health.server'
import { env } from '@/utils/environment.server'

export async function GET() {
  try {
    const config = getCMSConfig()
    const healthCheck = await checkCMSHealth()
    
    // Get environment variables (safely)
    const envVars = {
      CMS_BASE_URL: env('CMS_BASE_URL'),
      NODE_ENV: env('NODE_ENV'),
      // Don't expose the secret in the response
      CMS_SECRET_SET: !!env('CMS_SECRET')
    }

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      config,
      healthCheck,
      environment: envVars,
      recommendations: getRecommendations(healthCheck, config)
    })
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

function getRecommendations(healthCheck: any, config: any): string[] {
  const recommendations: string[] = []
  
  if (!healthCheck.isHealthy) {
    recommendations.push('CMS is not responding - check if the CMS server is running')
    
    if (healthCheck.error?.includes('timeout')) {
      recommendations.push('Request timed out - consider increasing timeout or checking network connectivity')
    }
    
    if (healthCheck.error?.includes('ECONNREFUSED')) {
      recommendations.push('Connection refused - verify CMS_BASE_URL is correct and CMS server is running')
    }
    
    if (healthCheck.error?.includes('404')) {
      recommendations.push('API endpoint not found - verify the CMS API is properly configured')
    }
  }
  
  if (config.timeout > 15000) {
    recommendations.push('Consider reducing timeout for better user experience')
  }
  
  if (!config.baseUrl.startsWith('https://') && process.env.NODE_ENV === 'production') {
    recommendations.push('Use HTTPS in production for security')
  }
  
  return recommendations
}
