"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, Play, Heart, Coffee, Camera, Sparkles } from "lucide-react"
import { useTranslations } from "next-intl"

export function HeroSection() {
  const t = useTranslations("hero")

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-secondary/20 pt-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight mb-6">
              {t("title")}
              <span className="text-primary block">Time With Tuuli</span>
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl">{t("description")}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl px-8 py-4 text-lg shadow-soft hover:shadow-soft-hover transition-all duration-300"
              >
                {t("cta")}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground rounded-xl px-8 py-4 text-lg transition-all duration-300 bg-transparent"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Story
              </Button>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative">
            <div className="relative rounded-3xl overflow-hidden shadow-soft-hover">
              <img
                src="/placeholder.jpg"
                alt="Tuuli's creative workspace"
                className="w-full h-auto object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent"></div>
            </div>
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-accent/90 rounded-full flex items-center justify-center shadow-soft animate-bounce">
              <Heart className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -bottom-6 -left-6 w-14 h-14 bg-secondary/90 rounded-full flex items-center justify-center shadow-soft animate-pulse">
              <Coffee className="w-7 h-7 text-foreground" />
            </div>
            <div className="absolute top-1/3 -left-8 w-12 h-12 bg-primary/80 rounded-full flex items-center justify-center shadow-soft animate-bounce delay-300">
              <Camera className="w-6 h-6 text-white" />
            </div>
            <div className="absolute bottom-1/4 -right-8 w-10 h-10 bg-accent/70 rounded-full flex items-center justify-center shadow-soft animate-pulse delay-500">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
