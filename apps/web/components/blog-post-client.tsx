"use client"

import { useState } from "react"
import { SocialShare } from "@/components/social-share"
import { LoadMoreButton } from "@/components/load-more-button"
import { SectionLoading } from "@/components/loading-spinner"

interface BlogPost {
  id: string | number;
  title: string;
  content?: string;
  slug?: string;
  publishedAt?: string;
  meta?: {
    description?: string;
  };
}

interface BlogPostClientProps {
  initialPosts: BlogPost[];
  fallbackPosts: BlogPost[];
  hasMoreInitially: boolean;
}

export function BlogPostClient({ 
  initialPosts, 
  fallbackPosts, 
  hasMoreInitially 
}: BlogPostClientProps) {
  // Use CMS posts if available, otherwise fallback to example posts
  const postsToUse = initialPosts.length > 0 ? initialPosts : fallbackPosts;
  
  const [displayedPosts, setDisplayedPosts] = useState(postsToUse.slice(0, 6))
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(
    initialPosts.length > 0 ? hasMoreInitially : fallbackPosts.length > 6
  )
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // Initial loading simulation
  useState(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 800)
    return () => clearTimeout(timer)
  })

  const handleLoadMore = async () => {
    setIsLoading(true)
    
    try {
      if (initialPosts.length > 0) {
        // Load more from API
        const currentPage = Math.floor(displayedPosts.length / 6) + 1;
        const response = await fetch(`/api/posts?page=${currentPage + 1}&limit=6`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.posts && data.posts.length > 0) {
            setDisplayedPosts(prev => [...prev, ...data.posts]);
            setHasMore(data.pagination?.hasNextPage || false);
          } else {
            setHasMore(false);
          }
        } else {
          setHasMore(false);
        }
      } else {
        // Load more from fallback posts
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
        const currentLength = displayedPosts.length;
        const nextPosts = fallbackPosts.slice(currentLength, currentLength + 3);
        
        if (nextPosts.length > 0) {
          setDisplayedPosts(prev => [...prev, ...nextPosts]);
        }
        
        setHasMore(currentLength + nextPosts.length < fallbackPosts.length);
      }
    } catch (error) {
      console.error('Error loading more posts:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  }

  if (isInitialLoading) {
    return <SectionLoading text="Loading blog posts..." />
  }

  if (displayedPosts.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">No blog posts found</h2>
        <p className="text-gray-600">Check back later for new content!</p>
      </div>
    );
  }

  return (
    <div>
      {/* Display blog posts */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {displayedPosts.map((post) => (
          <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <h2 className="text-xl font-bold mb-3 line-clamp-2">{post.title}</h2>
              {post.content && (
                <p className="text-gray-600 mb-4 line-clamp-3">{post.content}</p>
              )}
              {post.meta?.description && (
                <p className="text-gray-600 mb-4 line-clamp-3">{post.meta.description}</p>
              )}
              {post.publishedAt && (
                <p className="text-sm text-gray-500 mb-4">
                  {new Date(post.publishedAt).toLocaleDateString()}
                </p>
              )}
              <SocialShare 
                title={post.title} 
                description={post.content || post.meta?.description || ''} 
              />
            </div>
          </article>
        ))}
      </div>
      
      {/* Load more button */}
      {hasMore && (
        <div className="mt-12 text-center">
          <LoadMoreButton onClick={handleLoadMore} isLoading={isLoading} />
        </div>
      )}
      
      {/* Status message */}
      {initialPosts.length === 0 && (
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">
            <strong>Note:</strong> Showing example posts. Connect to your CMS to display real blog content.
          </p>
        </div>
      )}
    </div>
  )
}
