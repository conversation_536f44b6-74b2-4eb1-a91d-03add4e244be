"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Star, Quote } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Director",
    company: "Creative Studio Co.",
    type: "business",
    content:
      "<PERSON><PERSON>'s content strategy transformed our brand presence. Her authentic approach to storytelling helped us connect with our audience in ways we never imagined.",
    rating: 5,
    avatar: "/placeholder-user.jpg",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Food Blogger",
    company: null,
    type: "individual",
    content:
      "Tuuli aitab mul leida inspiratsiooni igapäevaseks loominguks. Tema retseptid ja ellustiil on muutnud minu lähenemist toidule ja kodule.",
    rating: 5,
    avatar: "/placeholder-user.jpg",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "CEO",
    company: "TechStart Solutions",
    type: "business",
    content:
      "Working with <PERSON><PERSON> on our podcast series was incredible. Her storytelling skills and authentic voice helped us reach a completely new audience.",
    rating: 5,
    avatar: "/placeholder-user.jpg",
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Lifestyle Enthusiast",
    company: null,
    type: "individual",
    content:
      "Tuuli's community has become my daily inspiration. Her approach to mindful living and creativity has genuinely changed how I approach each day.",
    rating: 5,
    avatar: "/placeholder-user.jpg",
  },
  {
    id: 5,
    name: "Michael Rodriguez",
    role: "Brand Manager",
    company: "Wellness Collective",
    type: "business",
    content:
      "Tuuli's UGC campaigns delivered exceptional results. Her ability to create authentic content that resonates with our target audience is unmatched.",
    rating: 5,
    avatar: "/placeholder-user.jpg",
  },
]

export function FeedbackCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-16 bg-gradient-to-br from-background to-secondary/10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4 font-serif">What People Are Saying</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Hear from businesses and individuals who've been part of the Time With Tuuli journey
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial */}
          <div className="bg-card rounded-2xl p-8 md:p-12 shadow-lg border border-border/50 min-h-[300px] flex flex-col justify-center">
            <Quote className="h-8 w-8 text-primary mb-6 mx-auto" />

            <blockquote className="text-lg md:text-xl text-center text-foreground mb-8 leading-relaxed">
              "{testimonials[currentIndex].content}"
            </blockquote>

            <div className="flex flex-col items-center space-y-4">
              {/* Rating */}
              <div className="flex space-x-1">
                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-accent text-accent" />
                ))}
              </div>

              {/* Author Info */}
              <div className="text-center">
                <div className="font-semibold text-foreground text-lg">{testimonials[currentIndex].name}</div>
                <div className="text-muted-foreground">
                  {testimonials[currentIndex].role}
                  {testimonials[currentIndex].company && <span> • {testimonials[currentIndex].company}</span>}
                </div>
                <div className="text-sm text-primary font-medium mt-1 capitalize">
                  {testimonials[currentIndex].type}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm hover:bg-background border-border/50"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm hover:bg-background border-border/50"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-primary scale-110"
                    : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Business vs Individual Stats */}
        <div className="grid grid-cols-2 gap-8 mt-16 max-w-md mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">12+</div>
            <div className="text-muted-foreground">Business Partners</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">500+</div>
            <div className="text-muted-foreground">Community Members</div>
          </div>
        </div>
      </div>
    </section>
  )
}
