"use client"

import { useState } from "react"
import { Post } from "@/cms/PayloadCMSClient"
import { LoadMoreButton } from "@/components/load-more-button"
import { SocialShare } from "@/components/social-share"

interface BlogPostListProps {
  initialPosts: Post[]
  initialPagination?: {
    page: number
    limit: number
    totalPages: number
    totalDocs: number
    hasNextPage: boolean
    hasPrevPage: boolean
    nextPage?: number | null
    prevPage?: number | null
  }
  locale: string
}

interface BlogPostCardProps {
  post: Post
}

function BlogPostCard({ post }: BlogPostCardProps) {
  // Extract text content from rich text content
  const getTextContent = (content: any): string => {
    if (!content?.root?.children) return ""
    
    const extractText = (children: any[]): string => {
      return children.map(child => {
        if (child.text) return child.text
        if (child.children) return extractText(child.children)
        return ""
      }).join("")
    }
    
    return extractText(content.root.children)
  }

  const textContent = getTextContent(post.content)
  const excerpt = textContent.length > 200 ? textContent.substring(0, 200) + "..." : textContent
  
  // Format published date
  const publishedDate = post.publishedAt 
    ? new Date(post.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null

  return (
    <article className="mb-12 pb-8 border-b border-gray-200 last:border-b-0">
      {/* Hero Image */}
      {post.heroImage && typeof post.heroImage === 'object' && post.heroImage.url && (
        <div className="mb-6">
          <img
            src={post.heroImage.url}
            alt={post.heroImage.alt || post.title}
            className="w-full h-64 object-cover rounded-lg"
          />
        </div>
      )}

      {/* Post Header */}
      <header className="mb-4">
        <h2 className="text-3xl font-bold mb-2 hover:text-blue-600 transition-colors">
          <a href={`/blog/${post.slug}`}>
            {post.title}
          </a>
        </h2>
        
        {/* Meta information */}
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
          {publishedDate && (
            <time dateTime={post.publishedAt || undefined}>
              {publishedDate}
            </time>
          )}
          
          {/* Authors */}
          {post.populatedAuthors && post.populatedAuthors.length > 0 && (
            <div className="flex items-center gap-2">
              <span>by</span>
              {post.populatedAuthors.map((author, index) => (
                <span key={author.id}>
                  {author.name}
                  {index < post.populatedAuthors!.length - 1 && ", "}
                </span>
              ))}
            </div>
          )}
          
          {/* Categories */}
          {post.categories && post.categories.length > 0 && (
            <div className="flex items-center gap-2">
              {post.categories.map((category) => (
                <span 
                  key={typeof category === 'string' ? category : category.id}
                  className="bg-gray-100 px-2 py-1 rounded-full text-xs"
                >
                  {typeof category === 'string' ? category : category.title}
                </span>
              ))}
            </div>
          )}
        </div>
      </header>

      {/* Post Content */}
      <div className="mb-6">
        <p className="text-gray-700 leading-relaxed">
          {excerpt}
        </p>
        
        {textContent.length > 200 && (
          <a 
            href={`/blog/${post.slug}`}
            className="inline-block mt-3 text-blue-600 hover:text-blue-800 font-medium"
          >
            Read more →
          </a>
        )}
      </div>

      {/* Social Share */}
      <SocialShare 
        title={post.title} 
        description={post.meta?.description || excerpt}
        url={`/blog/${post.slug}`}
      />
    </article>
  )
}

export function BlogPostList({ initialPosts, initialPagination, locale }: BlogPostListProps) {
  const [posts, setPosts] = useState(initialPosts)
  const [pagination, setPagination] = useState(initialPagination)
  const [isLoading, setIsLoading] = useState(false)

  const handleLoadMore = async () => {
    if (!pagination?.hasNextPage || isLoading) return

    setIsLoading(true)
    
    try {
      // Call API route to get more posts
      const response = await fetch(`/api/posts?page=${pagination.nextPage}&limit=${pagination.limit}&locale=${locale}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch more posts')
      }
      
      const result = await response.json()
      
      if (result.success) {
        setPosts(prevPosts => [...prevPosts, ...result.data])
        setPagination(result.pagination)
      } else {
        console.error('Error loading more posts:', result.error)
      }
    } catch (error) {
      console.error('Error loading more posts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">No posts found</h2>
        <p className="text-gray-600">Check back later for new content!</p>
      </div>
    )
  }

  return (
    <div>
      {/* Posts List */}
      <div className="space-y-0">
        {posts.map((post) => (
          <BlogPostCard key={post.id} post={post} />
        ))}
      </div>

      {/* Load More Button */}
      {pagination?.hasNextPage && (
        <div className="text-center mt-12">
          <LoadMoreButton
            onLoadMore={handleLoadMore}
            isLoading={isLoading}
            hasMore={pagination.hasNextPage}
          />
        </div>
      )}
    </div>
  )
}
