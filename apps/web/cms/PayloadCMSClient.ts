import { components } from "./schema";
import { env } from "@/utils/environment.server";

// Base types for common parameters
interface BaseQueryParams {
  page?: number;
  limit?: number;
  depth?: number;
  locale?: string;
  'fallback-locale'?: string;
}

interface CreateQueryParams {
  depth?: number;
  locale?: string;
}

// Post-specific types using your schema
interface PostQueryParams extends BaseQueryParams {
  sort?: 'title' | '-title' | 'publishedAt' | '-publishedAt' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
  where?: Record<string, never> & (
    components["schemas"]["PostQueryOperations"] |
    components["schemas"]["PostQueryOperationsAnd"] |
    components["schemas"]["PostQueryOperationsOr"]
    );
}

// Type aliases for easier use
type Post = components["schemas"]["Post"];
type User = components["schemas"]["User"];
type Media = components["schemas"]["Media"];
type Page = components["schemas"]["Page"];
type Category = components["schemas"]["Category"];
type PostListResponse = components["responses"]["PostListResponse"]["content"]["application/json"];
type UserListResponse = components["responses"]["UserListResponse"]["content"]["application/json"];
type MediaListResponse = components["responses"]["MediaListResponse"]["content"]["application/json"];
type PageListResponse = components["responses"]["PageListResponse"]["content"]["application/json"];
type CategoryListResponse = components["responses"]["CategoryListResponse"]["content"]["application/json"];

interface ApiError {
  message: string;
  errors?: Array<{
    message: string;
    field?: string;
  }>;
}

// Client configuration
interface PayloadClientConfig {
  baseUrl?: string;
  apiKey: string;
  userSlug?: string;
  timeout?: number;
}

export class PayloadCMSClient {
  private baseUrl: string;
  private apiKey: string;
  private userSlug: string;
  private timeout: number;

  constructor(config?: Partial<PayloadClientConfig>) {
    this.baseUrl = config?.baseUrl || env('CMS_BASE_URL');
    this.apiKey = config?.apiKey || env('CMS_SECRET');
    this.userSlug = config?.userSlug || 'users';
    this.timeout = config?.timeout || 30000;

    // Ensure baseUrl doesn't end with slash
    this.baseUrl = this.baseUrl.replace(/\/$/, '');
  }

  /**
   * Build authorization header
   */
  private getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `${this.userSlug} API-Key ${this.apiKey}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Build query string from parameters
   */
  private buildQueryString(params?: Record<string, any>): string {
    if (!params) return '';

    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          searchParams.append(key, JSON.stringify(value));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  /**
   * Generic fetch method with error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getAuthHeaders(),
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          message: `HTTP ${response.status}: ${response.statusText}`,
        }));

        throw new Error(`API Error (${response.status}): ${errorData.message}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`Request timeout after ${this.timeout}ms`);
        }
        throw error;
      }

      throw new Error('Unknown error occurred');
    }
  }

  // ===== POSTS METHODS =====

  /**
   * Get all posts with optional filtering and pagination
   */
  async getPosts(params?: PostQueryParams): Promise<PostListResponse> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/posts${queryString}`);
  }

  /**
   * Get a specific post by ID
   */
  async getPost(id: string, params?: CreateQueryParams): Promise<Post> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/posts/${id}${queryString}`);
  }

  /**
   * Create a new post
   */
  async createPost(data: components["requestBodies"]["PostRequestBody"]["content"]["application/json"], params?: CreateQueryParams): Promise<components["responses"]["NewPostResponse"]["content"]["application/json"]> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/posts${queryString}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Update a post by ID
   */
  async updatePost(id: string, data: Partial<components["requestBodies"]["PostRequestBody"]["content"]["application/json"]>, params?: CreateQueryParams): Promise<Post> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/posts/${id}${queryString}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  /**
   * Delete a post by ID
   */
  async deletePost(id: string): Promise<{ message: string }> {
    return this.request(`/api/posts/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== USERS METHODS =====

  async getUsers(params?: BaseQueryParams): Promise<UserListResponse> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/users${queryString}`);
  }

  async getUser(id: string, params?: CreateQueryParams): Promise<User> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/users/${id}${queryString}`);
  }

  async createUser(data: components["requestBodies"]["UserRequestBody"]["content"]["application/json"], params?: CreateQueryParams): Promise<components["responses"]["NewUserResponse"]["content"]["application/json"]> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/users${queryString}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateUser(id: string, data: Partial<components["requestBodies"]["UserRequestBody"]["content"]["application/json"]>, params?: CreateQueryParams): Promise<User> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/users/${id}${queryString}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteUser(id: string): Promise<{ message: string }> {
    return this.request(`/api/users/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== MEDIA METHODS =====

  async getMedia(params?: BaseQueryParams): Promise<MediaListResponse> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/media${queryString}`);
  }

  async getMediaItem(id: string, params?: CreateQueryParams): Promise<Media> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/media/${id}${queryString}`);
  }

  async uploadMedia(file: File, params?: CreateQueryParams): Promise<components["responses"]["NewMediaResponse"]["content"]["application/json"]> {
    const queryString = this.buildQueryString(params);
    const formData = new FormData();
    formData.append('file', file);

    return this.request(`/api/media${queryString}`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `${this.userSlug} API-Key ${this.apiKey}`,
        // Don't set Content-Type for FormData, let the browser set it
      },
    });
  }

  async updateMedia(id: string, data: Partial<components["requestBodies"]["MediaRequestBody"]["content"]["application/json"]>, params?: CreateQueryParams): Promise<Media> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/media/${id}${queryString}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteMedia(id: string): Promise<{ message: string }> {
    return this.request(`/api/media/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== PAGES METHODS =====

  async getPages(params?: BaseQueryParams): Promise<PageListResponse> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/pages${queryString}`);
  }

  async getPage(id: string, params?: CreateQueryParams): Promise<Page> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/pages/${id}${queryString}`);
  }

  async createPage(data: components["requestBodies"]["PageRequestBody"]["content"]["application/json"], params?: CreateQueryParams): Promise<components["responses"]["NewPageResponse"]["content"]["application/json"]> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/pages${queryString}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updatePage(id: string, data: Partial<components["requestBodies"]["PageRequestBody"]["content"]["application/json"]>, params?: CreateQueryParams): Promise<Page> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/pages/${id}${queryString}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deletePage(id: string): Promise<{ message: string }> {
    return this.request(`/api/pages/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== CATEGORIES METHODS =====

  async getCategories(params?: BaseQueryParams): Promise<CategoryListResponse> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/categories${queryString}`);
  }

  async getCategory(id: string, params?: CreateQueryParams): Promise<Category> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/categories/${id}${queryString}`);
  }

  async createCategory(data: components["requestBodies"]["CategoryRequestBody"]["content"]["application/json"], params?: CreateQueryParams): Promise<components["responses"]["NewCategoryResponse"]["content"]["application/json"]> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/categories${queryString}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateCategory(id: string, data: Partial<components["requestBodies"]["CategoryRequestBody"]["content"]["application/json"]>, params?: CreateQueryParams): Promise<Category> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/categories/${id}${queryString}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteCategory(id: string): Promise<{ message: string }> {
    return this.request(`/api/categories/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== PAYLOAD-SPECIFIC METHODS =====

  async getPayloadJobs(params?: BaseQueryParams): Promise<any> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/payload-jobs${queryString}`);
  }

  async getPayloadPreferences(params?: BaseQueryParams): Promise<any> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/payload-preferences${queryString}`);
  }

  async getPayloadMigrations(params?: BaseQueryParams): Promise<any> {
    const queryString = this.buildQueryString(params);
    return this.request(`/api/payload-migrations${queryString}`);
  }

  // ===== UTILITY METHODS =====

  /**
   * Test the connection to the CMS
   */
  async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      // Try to fetch users with a limit of 1 to test connection
      await this.getUsers({ limit: 1 });
      return { status: 'ok', message: 'Successfully connected to Payload CMS' };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get the current configuration
   */
  getConfig(): { baseUrl: string; userSlug: string; timeout: number } {
    return {
      baseUrl: this.baseUrl,
      userSlug: this.userSlug,
      timeout: this.timeout,
    };
  }
}

// Export a default instance with optimized settings
export const payloadClient = new PayloadCMSClient({
  timeout: 10000, // Reduce timeout to 10 seconds for faster failure
});

// Export types for use in other files
export type {
  Post,
  User,
  Media,
  Page,
  Category,
  PostListResponse,
  UserListResponse,
  MediaListResponse,
  PageListResponse,
  CategoryListResponse,
  PostQueryParams,
  BaseQueryParams,
  CreateQueryParams,
  components
};
